name: turathna
description: "Turathna - Discover Algeria's Cultural Heritage"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  firebase_core: ^2.24.2
  pdf: ^3.11.3
  open_file: ^3.5.10
  shared_preferences: ^2.5.3
  cloud_firestore: ^4.13.6
  provider: ^6.1.1
  qr_flutter: ^4.1.0
  flutter_localizations:
    sdk: flutter
  video_player: ^2.7.0
  intl: ^0.19.0
  flutter_lints: ^5.0.0
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  carousel_slider: ^5.0.0
  uuid: ^4.5.1
  firebase_core_web: ^2.10.0
  path_provider: ^2.1.2
  share_plus: ^7.2.1
  cross_file: ^0.3.3+8
  sqflite: ^2.3.0
  path: ^1.9.0
  http: ^1.4.0
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  permission_handler: ^11.2.0
  youtube_player_flutter: ^9.0.3



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/images/bordj_logo.jpg
    - assets/images/bordj1.jpg
    - assets/images/bordj2.jpg
    - assets/images/bordj3.jpg
    - assets/images/bordj4.jpg
    - assets/images/bordj5.jpg
    - assets/lang/en.json
    - assets/lang/fr.json
    - assets/lang/ar.json


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package